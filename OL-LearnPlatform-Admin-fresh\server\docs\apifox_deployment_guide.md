# Apifox 接口部署详细步骤指南

## 前置准备

### 1. 确保服务器运行
```bash
# 在项目根目录下启动服务器
cd OL-LearnPlatform-Admin-fresh/server
go run main.go
```

服务器启动后会监听 `http://localhost:8000`

### 2. 验证服务器状态
访问 `http://localhost:8000/swagger` 查看API文档，确认服务器正常运行。

## Apifox 配置步骤

### 第一步：创建项目

1. 打开 Apifox 客户端
2. 点击 **"新建项目"**
3. 输入项目名称：`作业管理系统`
4. 选择项目类型：**HTTP 项目**
5. 点击 **"确定"** 创建项目

### 第二步：配置环境

1. 在项目中点击 **"环境管理"**
2. 点击 **"新建环境"**
3. 环境名称：`本地开发环境`
4. 添加环境变量：
   - **变量名**: `baseUrl`
   - **变量值**: `http://localhost:8000`
   - **描述**: 服务器基础地址

### 第三步：配置全局认证

1. 点击项目设置 → **"认证"**
2. 选择认证类型：**Bearer Token**
3. Token 位置：**Header**
4. Header 名称：`Authorization`
5. Token 前缀：`Bearer `

### 第四步：创建接口分组

1. 在接口管理中右键点击根目录
2. 选择 **"新建分组"**
3. 分组名称：`作业模块`
4. 描述：`学生作业相关接口`

### 第五步：创建具体接口

#### 5.1 创建"获取作业列表"接口

1. 右键点击 **"作业模块"** 分组
2. 选择 **"新建接口"**
3. 填写接口信息：
   - **接口名称**: `获取作业列表`
   - **请求方法**: `GET`
   - **请求路径**: `{{baseUrl}}/api/homework/list`
   - **接口描述**: `获取当前用户的作业列表，支持状态筛选和分页`

4. 配置请求参数（Query 参数）：
   - `status`: string，可选，作业状态筛选
   - `page`: integer，可选，页码，默认1
   - `pageSize`: integer，可选，每页数量，默认10

5. 配置响应示例：
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "title": "第一章作业",
        "description": "完成第一章的练习题",
        "teacherName": "张老师",
        "status": "pending"
      }
    ],
    "total": 50,
    "page": 1,
    "pageSize": 10
  }
}
```

#### 5.2 创建"获取作业详情"接口

1. 新建接口：
   - **接口名称**: `获取作业详情`
   - **请求方法**: `GET`
   - **请求路径**: `{{baseUrl}}/api/homework/detail`

2. 配置请求参数（Query 参数）：
   - `id`: integer，必填，作业ID

#### 5.3 创建"提交作业"接口

1. 新建接口：
   - **接口名称**: `提交作业`
   - **请求方法**: `POST`
   - **请求路径**: `{{baseUrl}}/api/homework/submit`

2. 配置请求体（JSON）：
```json
{
  "homeworkId": 1,
  "content": "这是我的作业答案",
  "attachmentIds": [1, 2, 3]
}
```

#### 5.4 创建"上传作业文件"接口

1. 新建接口：
   - **接口名称**: `上传作业文件`
   - **请求方法**: `POST`
   - **请求路径**: `{{baseUrl}}/api/homework/upload`
   - **Content-Type**: `multipart/form-data`

2. 配置请求参数（Form Data）：
   - `homeworkId`: integer，必填，作业ID
   - `file`: file，必填，要上传的文件

#### 5.5 创建"下载作业文件"接口

1. 新建接口：
   - **接口名称**: `下载作业文件`
   - **请求方法**: `GET`
   - **请求路径**: `{{baseUrl}}/api/homework/download`

2. 配置请求参数（Query 参数）：
   - `fileId`: integer，必填，文件ID

### 第六步：配置测试数据

1. 在环境管理中添加测试用的 Token：
   - **变量名**: `authToken`
   - **变量值**: `your_jwt_token_here`
   - **描述**: 用户认证Token

2. 在全局认证中使用变量：
   - Token 值：`{{authToken}}`

### 第七步：测试接口

#### 7.1 获取认证Token

首先需要通过登录接口获取有效的JWT Token，然后更新环境变量中的 `authToken`。

#### 7.2 测试接口调用

1. 选择要测试的接口
2. 点击 **"发送"** 按钮
3. 查看响应结果
4. 验证返回数据格式是否正确

### 第八步：创建接口测试用例

1. 在接口详情页点击 **"测试用例"**
2. 点击 **"新建用例"**
3. 设置用例名称和测试数据
4. 配置断言规则：
   - 响应状态码 = 200
   - 响应体.code = 0
   - 响应体.data 不为空

### 第九步：批量测试

1. 选择 **"作业模块"** 分组
2. 点击 **"批量测试"**
3. 选择要测试的接口
4. 点击 **"开始测试"**
5. 查看测试报告

## 常见问题解决

### 1. 连接失败
- 检查服务器是否启动：`http://localhost:8000`
- 检查防火墙设置
- 确认端口8000未被占用

### 2. 认证失败
- 确认JWT Token是否有效
- 检查Token格式：`Bearer your_token`
- 验证Token是否过期

### 3. 参数错误
- 检查参数类型和格式
- 确认必填参数是否提供
- 验证参数值是否符合要求

### 4. 文件上传失败
- 确认Content-Type为multipart/form-data
- 检查文件大小限制
- 验证文件格式是否支持

## 导出和分享

### 导出接口文档
1. 选择项目
2. 点击 **"导出"**
3. 选择导出格式（Markdown、HTML等）
4. 点击 **"导出"**

### 分享项目
1. 点击项目设置
2. 选择 **"团队协作"**
3. 邀请团队成员
4. 设置权限级别

## 快速测试脚本

### 使用curl命令测试接口

```bash
# 1. 测试获取作业列表（无需认证的测试）
curl -X GET "http://localhost:8000/api/homework/list?page=1&pageSize=10" \
  -H "Content-Type: application/json"

# 2. 测试获取作业详情
curl -X GET "http://localhost:8000/api/homework/detail?id=1" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"

# 3. 测试提交作业
curl -X POST "http://localhost:8000/api/homework/submit" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -d '{
    "homeworkId": 1,
    "content": "这是测试作业内容",
    "attachmentIds": []
  }'

# 4. 测试文件上传
curl -X POST "http://localhost:8000/api/homework/upload" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -F "homeworkId=1" \
  -F "file=@/path/to/your/file.pdf"
```

### Postman 导入配置

如果您使用Postman，可以导入以下JSON配置：

```json
{
  "info": {
    "name": "作业管理系统API",
    "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"
  },
  "variable": [
    {
      "key": "baseUrl",
      "value": "http://localhost:8000"
    }
  ],
  "auth": {
    "type": "bearer",
    "bearer": [
      {
        "key": "token",
        "value": "{{authToken}}"
      }
    ]
  }
}
```

通过以上步骤，您就可以在Apifox中完整地配置和测试作业模块的所有API接口了。
