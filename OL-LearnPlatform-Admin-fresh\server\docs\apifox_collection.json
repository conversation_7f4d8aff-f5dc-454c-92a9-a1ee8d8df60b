{
  "info": {
    "name": "作业管理系统API",
    "description": "学生作业模块相关接口",
    "version": "1.0.0",
    "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"
  },
  "variable": [
    {
      "key": "baseUrl",
      "value": "http://localhost:8000",
      "type": "string"
    },
    {
      "key": "authToken",
      "value": "your_jwt_token_here",
      "type": "string"
    }
  ],
  "auth": {
    "type": "bearer",
    "bearer": [
      {
        "key": "token",
        "value": "{{authToken}}",
        "type": "string"
      }
    ]
  },
  "item": [
    {
      "name": "作业模块",
      "item": [
        {
          "name": "获取作业列表",
          "request": {
            "method": "GET",
            "header": [
              {
                "key": "Content-Type",
                "value": "application/json"
              }
            ],
            "url": {
              "raw": "{{baseUrl}}/api/homework/list?status=pending&page=1&pageSize=10",
              "host": ["{{baseUrl}}"],
              "path": ["api", "homework", "list"],
              "query": [
                {
                  "key": "status",
                  "value": "pending",
                  "description": "作业状态筛选：pending-未完成，completed-已完成，空值-全部"
                },
                {
                  "key": "page",
                  "value": "1",
                  "description": "页码，从1开始，默认1"
                },
                {
                  "key": "pageSize",
                  "value": "10",
                  "description": "每页数量，默认10条"
                }
              ]
            }
          },
          "response": [
            {
              "name": "成功响应",
              "originalRequest": {
                "method": "GET",
                "header": [],
                "url": {
                  "raw": "{{baseUrl}}/api/homework/list?status=pending&page=1&pageSize=10",
                  "host": ["{{baseUrl}}"],
                  "path": ["api", "homework", "list"],
                  "query": [
                    {"key": "status", "value": "pending"},
                    {"key": "page", "value": "1"},
                    {"key": "pageSize", "value": "10"}
                  ]
                }
              },
              "status": "OK",
              "code": 200,
              "_postman_previewlanguage": "json",
              "header": [
                {
                  "key": "Content-Type",
                  "value": "application/json"
                }
              ],
              "cookie": [],
              "body": "{\n  \"code\": 0,\n  \"message\": \"success\",\n  \"data\": {\n    \"list\": [\n      {\n        \"id\": 1,\n        \"title\": \"第一章作业\",\n        \"description\": \"完成第一章的练习题\",\n        \"images\": [\"http://example.com/image1.jpg\"],\n        \"teacherName\": \"张老师\",\n        \"teacherAvatar\": \"http://example.com/avatar.jpg\",\n        \"publishTime\": \"2024-01-01 10:00:00\",\n        \"dueTime\": \"2024-01-07 23:59:59\",\n        \"status\": \"pending\",\n        \"submitTime\": null,\n        \"score\": 0,\n        \"maxScore\": 100,\n        \"lessonId\": 1,\n        \"lessonName\": \"数学基础\"\n      }\n    ],\n    \"total\": 50,\n    \"page\": 1,\n    \"pageSize\": 10\n  }\n}"
            }
          ]
        },
        {
          "name": "获取作业详情",
          "request": {
            "method": "GET",
            "header": [
              {
                "key": "Content-Type",
                "value": "application/json"
              }
            ],
            "url": {
              "raw": "{{baseUrl}}/api/homework/detail?id=1",
              "host": ["{{baseUrl}}"],
              "path": ["api", "homework", "detail"],
              "query": [
                {
                  "key": "id",
                  "value": "1",
                  "description": "作业ID"
                }
              ]
            }
          },
        {
          "name": "提交作业",
          "request": {
            "method": "POST",
            "header": [
              {
                "key": "Content-Type",
                "value": "application/json"
              }
            ],
            "body": {
              "mode": "raw",
              "raw": "{\n  \"homeworkId\": 1,\n  \"content\": \"这是我的作业答案...\",\n  \"attachmentIds\": [1, 2, 3]\n}",
              "options": {
                "raw": {
                  "language": "json"
                }
              }
            },
            "url": {
              "raw": "{{baseUrl}}/api/homework/submit",
              "host": ["{{baseUrl}}"],
              "path": ["api", "homework", "submit"]
            }
          },
          "response": [
            {
              "name": "成功响应",
              "status": "OK",
              "code": 200,
              "_postman_previewlanguage": "json",
              "header": [
                {
                  "key": "Content-Type",
                  "value": "application/json"
                }
              ],
              "body": "{\n  \"code\": 0,\n  \"message\": \"提交成功\",\n  \"data\": {\n    \"submitId\": 123\n  }\n}"
            }
          ]
        },
        {
          "name": "上传作业文件",
          "request": {
            "method": "POST",
            "header": [],
            "body": {
              "mode": "formdata",
              "formdata": [
                {
                  "key": "homeworkId",
                  "value": "1",
                  "type": "text",
                  "description": "作业ID"
                },
                {
                  "key": "file",
                  "type": "file",
                  "src": [],
                  "description": "要上传的文件"
                }
              ]
            },
            "url": {
              "raw": "{{baseUrl}}/api/homework/upload",
              "host": ["{{baseUrl}}"],
              "path": ["api", "homework", "upload"]
            }
          },
          "response": [
            {
              "name": "成功响应",
              "status": "OK",
              "code": 200,
              "_postman_previewlanguage": "json",
              "header": [
                {
                  "key": "Content-Type",
                  "value": "application/json"
                }
              ],
              "body": "{\n  \"code\": 0,\n  \"message\": \"上传成功\",\n  \"data\": {\n    \"fileId\": 456,\n    \"fileName\": \"homework.pdf\",\n    \"fileUrl\": \"http://localhost:8000/uploads/homework.pdf\",\n    \"fileSize\": 1024000\n  }\n}"
            }
          ]
        },
        {
          "name": "下载作业文件",
          "request": {
            "method": "GET",
            "header": [],
            "url": {
              "raw": "{{baseUrl}}/api/homework/download?fileId=456",
              "host": ["{{baseUrl}}"],
              "path": ["api", "homework", "download"],
              "query": [
                {
                  "key": "fileId",
                  "value": "456",
                  "description": "文件ID"
                }
              ]
            }
          },
          "response": [
            {
              "name": "成功响应",
              "status": "OK",
              "code": 200,
              "_postman_previewlanguage": "raw",
              "header": [
                {
                  "key": "Content-Type",
                  "value": "application/octet-stream"
                },
                {
                  "key": "Content-Disposition",
                  "value": "attachment; filename=homework.pdf"
                }
              ],
              "body": "文件二进制数据流"
            }
          ]
        },
          "response": [
            {
              "name": "成功响应",
              "status": "OK",
              "code": 200,
              "_postman_previewlanguage": "json",
              "header": [
                {
                  "key": "Content-Type",
                  "value": "application/json"
                }
              ],
              "body": "{\n  \"code\": 0,\n  \"message\": \"success\",\n  \"data\": {\n    \"id\": 1,\n    \"title\": \"第一章作业\",\n    \"description\": \"完成第一章的练习题\",\n    \"images\": [\"http://example.com/image1.jpg\"],\n    \"teacherName\": \"张老师\",\n    \"teacherAvatar\": \"http://example.com/avatar.jpg\",\n    \"publishTime\": \"2024-01-01 10:00:00\",\n    \"dueTime\": \"2024-01-07 23:59:59\",\n    \"status\": \"pending\",\n    \"submitTime\": null,\n    \"score\": 0,\n    \"maxScore\": 100,\n    \"lessonId\": 1,\n    \"lessonName\": \"数学基础\",\n    \"requirements\": \"请完成以下题目：1. 计算题... 2. 应用题...\",\n    \"attachments\": [\"http://example.com/homework.pdf\"],\n    \"submitContent\": \"\",\n    \"submitFiles\": [],\n    \"teacherComment\": \"\"\n  }\n}"
            }
          ]
        }
