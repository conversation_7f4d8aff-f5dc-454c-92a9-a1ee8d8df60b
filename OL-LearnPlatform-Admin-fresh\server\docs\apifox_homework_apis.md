# 作业模块 API 接口文档 - Apifox 配置指南

## 基础信息

- **服务器地址**: `http://localhost:8000`
- **API前缀**: `/api`
- **认证方式**: <PERSON><PERSON> (JWT)

## 接口列表

### 1. 获取作业列表

**接口信息**
- **方法**: GET
- **路径**: `/api/homework/list`
- **描述**: 获取当前用户的作业列表，支持状态筛选和分页

**请求参数 (Query参数)**
- `status`: string，可选，作业状态筛选：pending-未完成，completed-已完成，空值-全部
- `page`: integer，可选，页码，从1开始，默认1
- `pageSize`: integer，可选，每页数量，默认10条

**响应示例**
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "title": "第一章作业",
        "description": "完成第一章的练习题",
        "images": ["http://example.com/image1.jpg"],
        "teacherName": "张老师",
        "teacherAvatar": "http://example.com/avatar.jpg",
        "publishTime": "2024-01-01 10:00:00",
        "dueTime": "2024-01-07 23:59:59",
        "status": "pending",
        "submitTime": null,
        "score": 0,
        "maxScore": 100,
        "lessonId": 1,
        "lessonName": "数学基础"
      }
    ],
    "total": 50,
    "page": 1,
    "pageSize": 10
  }
}
```

### 2. 获取作业详情

**接口信息**
- **方法**: GET
- **路径**: `/api/homework/detail`
- **描述**: 获取指定作业的详细信息

**请求参数 (Query参数)**
- `id`: integer，必填，作业ID

**响应示例**
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1,
    "title": "第一章作业",
    "description": "完成第一章的练习题",
    "images": ["http://example.com/image1.jpg"],
    "teacherName": "张老师",
    "teacherAvatar": "http://example.com/avatar.jpg",
    "publishTime": "2024-01-01 10:00:00",
    "dueTime": "2024-01-07 23:59:59",
    "status": "pending",
    "submitTime": null,
    "score": 0,
    "maxScore": 100,
    "lessonId": 1,
    "lessonName": "数学基础",
    "requirements": "请完成以下题目：1. 计算题... 2. 应用题...",
    "attachments": ["http://example.com/homework.pdf"],
    "submitContent": "",
    "submitFiles": [],
    "teacherComment": ""
  }
}
```

### 3. 提交作业

**接口信息**
- **方法**: POST
- **路径**: `/api/homework/submit`
- **描述**: 提交作业内容和附件

**请求参数 (JSON Body)**
```json
{
  "homeworkId": 1,                    // integer，必填，作业ID
  "content": "这是我的作业答案...",      // string，必填，作业内容
  "attachmentIds": [1, 2, 3]         // array，可选，附件ID列表
}
```

**响应示例**
```json
{
  "code": 0,
  "message": "提交成功",
  "data": {
    "submitId": 123
  }
}
```

### 4. 上传作业文件

**接口信息**
- **方法**: POST
- **路径**: `/api/homework/upload`
- **描述**: 上传作业相关文件
- **Content-Type**: multipart/form-data

**请求参数 (Form Data)**
- `homeworkId`: integer，必填，作业ID
- `file`: file，必填，要上传的文件

**响应示例**
```json
{
  "code": 0,
  "message": "上传成功",
  "data": {
    "fileId": 456,
    "fileName": "homework.pdf",
    "fileUrl": "http://localhost:8000/uploads/homework.pdf",
    "fileSize": 1024000
  }
}
```

### 5. 下载作业文件

**接口信息**
- **方法**: GET
- **路径**: `/api/homework/download`
- **描述**: 下载作业相关文件

**请求参数 (Query参数)**
- `fileId`: integer，必填，文件ID

**响应**: 直接返回文件流，浏览器会自动下载文件

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| -1 | 系统错误 |
| 401 | 未登录或token无效 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 422 | 参数验证失败 |

## 认证说明

所有接口都需要在请求头中携带JWT token：

```
Authorization: Bearer your_jwt_token_here
```

获取token的方式请参考用户登录接口文档。
